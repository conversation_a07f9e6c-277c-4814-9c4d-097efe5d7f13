import { useEffect } from 'react';
import {
  ANALYTICS_EVENTS,
  trackEvent,
  trackExternalLink,
  throttle
} from './analyticsUtils';

const Analytics = () => {
  useEffect(() => {
    // Initialize Umami
    const umamiScript = document.createElement('script');
    umamiScript.defer = true;
    umamiScript.src = "https://cloud.umami.is/script.js";
    umamiScript.setAttribute('data-website-id', 'ddc4d29c-df03-4c6b-84be-02fb79bbf76e');
    document.head.appendChild(umamiScript);

    // Add global click listener for external links only
    const handleLinkClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      const link = target.closest('a');
      if (link && link.hostname !== window.location.hostname) {
        const linkUrl = link.href;
        const linkText = link.textContent || 'Link';
        trackExternalLink(linkUrl, linkText);
      }
    };

    document.addEventListener('click', handleLinkClick);

    // Simplified scroll tracking - only 75% engagement milestone
    let scroll75Triggered = false;

    const handleScroll = throttle(() => {
      if (scroll75Triggered) return;

      const totalHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrolled = window.scrollY;
      const scrollPercentage = totalHeight > 0 ? Math.round((scrolled / totalHeight) * 100) : 0;

      if (scrollPercentage >= 75) {
        trackEvent(ANALYTICS_EVENTS.SCROLL_75, {
          category: 'Engagement',
          label: '75% Scroll',
          value: 75
        });
        scroll75Triggered = true;
      }
    }, 500); // Reduced frequency

    document.addEventListener('scroll', handleScroll);

    // Cleanup function
    return () => {
      document.head.removeChild(umamiScript);
      document.removeEventListener('click', handleLinkClick);
      document.removeEventListener('scroll', handleScroll);
    };
  }, []); 

  return null;
};

export default Analytics;