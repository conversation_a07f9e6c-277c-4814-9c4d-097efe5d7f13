// Analytics Events Constants
export const ANALYTICS_EVENTS = {
  SCROLL_75: 'scroll_75',
  LEAD_GENERATION: 'lead_generation',
  CTA_CLICK: 'cta_click',
  FORM_SUBMISSION: 'form_submission',
  FILE_DOWNLOAD: 'file_download',
  EXTERNAL_LINK_CLICK: 'external_link_click',
  CONSULTATION_REQUEST: 'consultation_request',
} as const;

// Utility function for throttling
export const throttle = <T extends (...args: any[]) => any>(func: T, delay: number) => {
  let timeoutId: NodeJS.Timeout | null = null;
  let lastExecTime = 0;
  
  return function (this: ThisParameterType<T>, ...args: Parameters<T>) {
    const currentTime = Date.now();
    
    if (currentTime - lastExecTime > delay) {
      func.apply(this, args);
      lastExecTime = currentTime;
    } else {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      timeoutId = setTimeout(() => {
        func.apply(this, args);
        lastExecTime = Date.now();
      }, delay - (currentTime - lastExecTime));
    }
  };
};

// Declare global umami and posthog interfaces
declare global {
  interface Window {
    umami?: {
      track: (event: string, data?: Record<string, any>) => void;
    };
    posthog?: {
      capture: (event: string, properties?: Record<string, any>) => void;
    };
  }
}

// Core tracking function
export const trackEvent = (eventName: string, properties: Record<string, any> = {}) => {
  try {
    // Track with Umami
    if (window.umami?.track) {
      window.umami.track(eventName, properties);
    }

    // Track with PostHog
    if (window.posthog?.capture) {
      window.posthog.capture(eventName, properties);
    }

    // Development logging
    if (import.meta.env.MODE === 'development') {
      console.log('Analytics Event:', eventName, properties);
    }
  } catch (error) {
    console.error('Analytics tracking error:', error);
  }
};

// High-value conversion tracking functions
export const trackLeadGeneration = (formType: string, source: string) => {
  trackEvent(ANALYTICS_EVENTS.LEAD_GENERATION, {
    category: 'Conversion',
    label: 'Lead Generation',
    form_type: formType,
    source: source,
    value: 1
  });
};

export const trackConsultationRequest = (serviceType: string, source: string) => {
  trackEvent(ANALYTICS_EVENTS.CONSULTATION_REQUEST, {
    category: 'Conversion',
    label: 'Consultation Request',
    service_type: serviceType,
    source: source,
    value: 1
  });
};

// Essential user action tracking functions
export const trackCTAClick = (ctaName: string, location: string) => {
  trackEvent(ANALYTICS_EVENTS.CTA_CLICK, {
    category: 'Engagement',
    label: 'CTA Click',
    cta_name: ctaName,
    location: location
  });
};

export const trackFormSubmission = (formName: string, success: boolean) => {
  trackEvent(ANALYTICS_EVENTS.FORM_SUBMISSION, {
    category: 'Conversion',
    label: 'Form Submission',
    form_name: formName,
    success: success,
    value: success ? 1 : 0
  });
};

export const trackFileDownload = (fileName: string, fileType: string) => {
  trackEvent(ANALYTICS_EVENTS.FILE_DOWNLOAD, {
    category: 'Engagement',
    label: 'File Download',
    file_name: fileName,
    file_type: fileType
  });
};

export const trackExternalLink = (linkUrl: string, linkText: string) => {
  trackEvent(ANALYTICS_EVENTS.EXTERNAL_LINK_CLICK, {
    category: 'Engagement',
    label: 'External Link Click',
    link_url: linkUrl,
    link_text: linkText
  });
};
