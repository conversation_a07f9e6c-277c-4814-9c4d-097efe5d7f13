import React from "react";
import { Helmet } from "react-helmet-async";
import { motion } from "framer-motion";
import {
  Shield,
  Server,
  Cloud,
  Lock,
  CheckCircle,
  TrendingUp,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ButtonLink } from "@/components/ui/button-link";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import CanonicalUrl from "@/components/CanonicalUrl";
import FilloutEmbed from "@/components/FilloutEmbed";
import { trackFileDownload } from "@/components/analyticsUtils";
import PartnerLogo from "@/components/PartnerLogo";
import {
  createServiceSchema,
  createLocalBusinessSchema,
  createReviewSchema,
} from "@/utils/schemaData";
import {
  services,
  partnerLogos,
  keyStats,
  industries,
} from "@/components/home/<USER>";

const Index: React.FC = () => {
  // Create schema data
  const localBusinessSchema = createLocalBusinessSchema({
    name: "Teclara Technologies",
    description:
      "Enterprise-grade cybersecurity and managed IT services for Canadian SMBs",
    address: {
      street: "123 Business Street",
      city: "Oakville",
      region: "ON",
      postalCode: "L6H 0C3",
      country: "CA",
    },
    geo: {
      latitude: 43.4675,
      longitude: -79.6877,
    },
    phone: "******-123-4567",
    email: "<EMAIL>",
    priceRange: "$$",
    openingHours: [
      "Monday 09:00-17:00",
      "Tuesday 09:00-17:00",
      "Wednesday 09:00-17:00",
      "Thursday 09:00-17:00",
      "Friday 09:00-17:00",
    ],
  });

  const serviceSchema = createServiceSchema(
    "Enterprise IT & Security Solutions",
    "Enterprise-grade cybersecurity, managed IT, and business continuity solutions for SMBs. 24/7 monitoring, compliance assistance, and expert support.",
    "IT Services & Security",
    {
      areaServed: [
        "Oakville",
        "Hamilton",
        "Milton",
        "St. Catharines",
        "Halton Region",
      ],
      serviceOutput: [
        "24/7 Security Monitoring",
        "Compliance Support",
        "Expert IT Support",
        "Proactive Maintenance",
        "Business Continuity",
        "Cloud Solutions",
      ],
    }
  );

  const reviewSchema = createReviewSchema([
    {
      author: "John Smith",
      reviewBody:
        "Teclara has transformed our IT infrastructure. Their proactive approach and 24/7 support have been invaluable.",
      reviewRating: 5,
      datePublished: "2024-03-15",
    },
    {
      author: "Sarah Johnson",
      reviewBody:
        "Outstanding cybersecurity services. They've helped us achieve and maintain compliance while improving our security posture.",
      reviewRating: 5,
      datePublished: "2024-03-10",
    },
  ]);

  const handleBrochureDownload = React.useCallback(() => {
    trackFileDownload("Teclara-Overview-Deck.pdf", "pdf");
  }, []);

  // Trust indicators data
  const trustIndicators = [
    { icon: Shield, text: "Enterprise-Grade Security" },
    { icon: Server, text: "Managed IT" },
    { icon: Cloud, text: "Cloud Solutions" },
    { icon: Lock, text: "Compliance" },
  ];

  // Double the logos array for seamless loop
  const doubledLogos = [...partnerLogos, ...partnerLogos];

  return (
    <>
      <Helmet>
        <title>Teclara - Enterprise Cybersecurity & Managed IT for SMBs</title>
        <meta
          name="description"
          content="Protect your business with enterprise-grade cybersecurity and managed IT services. 24/7 SOC monitoring, compliance support, and proactive threat detection for Canadian SMBs."
        />

        {/* Page specific Open Graph */}
        <meta
          property="og:title"
          content="Teclara - Enterprise Cybersecurity & Managed IT for SMBs"
        />
        <meta
          property="og:description"
          content="Enterprise-grade cybersecurity and IT solutions with 24/7 monitoring, compliance support, and expert local service."
        />
        <meta property="og:url" content="https://teclara.tech/" />
        <meta property="og:type" content="website" />
        <meta
          property="og:image"
          content="https://teclara.tech/uploads/teclara_logo_black_text.png"
        />
        <meta property="og:image:alt" content="Teclara Technologies Logo" />

        {/* Page specific Twitter */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta
          name="twitter:title"
          content="Teclara - Enterprise Cybersecurity & Managed IT for SMBs"
        />
        <meta
          name="twitter:description"
          content="Enterprise-grade cybersecurity, managed IT, and business continuity solutions. 24/7 monitoring, compliance assistance, and expert support for businesses of all sizes."
        />
        <meta
          name="twitter:image"
          content="https://teclara.tech/uploads/teclara_logo_black_text.png"
        />

        {/* Additional meta tags */}
        <meta
          name="keywords"
          content="cybersecurity, managed IT, business continuity, data protection, compliance, network security, cloud security, endpoint protection, Oakville, Hamilton, Milton, St. Catharines, Halton Region"
        />
        <meta name="author" content="Teclara Technologies" />
        <meta name="robots" content="index, follow" />
        <meta name="language" content="en-CA" />
        <meta name="geo.region" content="CA-ON" />
        <meta name="geo.placename" content="Oakville, Ontario" />
        <meta name="geo.position" content="43.4675;-79.6877" />
        <meta name="ICBM" content="43.4675, -79.6877" />

        {/* Resource hints */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="anonymous"
        />

        {/* Schema.org data */}
        <script type="application/ld+json">
          {JSON.stringify(localBusinessSchema)}
        </script>
        <script type="application/ld+json">
          {JSON.stringify(serviceSchema)}
        </script>
        <script type="application/ld+json">
          {JSON.stringify(reviewSchema)}
        </script>
      </Helmet>
      <CanonicalUrl />

      <div className="min-h-screen bg-gradient-to-br from-[#060d25] via-[#04081a] to-[#060d25] text-white">
        <Navbar />

        {/* Hero Section - Fullscreen */}
        <section className="relative min-h-screen flex items-center justify-center px-6 overflow-hidden pt-16 md:pt-20">
          {/* Brand Red Glow Background Effect - Subtle */}
          <div className="pointer-events-none absolute -top-32 left-1/2 -translate-x-1/2 w-[700px] h-[400px] bg-[#FF1717]/15 rounded-full blur-3xl z-0" />

          {/* Background Grid Pattern */}
          <div className="absolute inset-0 opacity-10">
            <div
              className="absolute inset-0"
              style={{
                backgroundImage: `
                linear-gradient(rgba(52, 197, 182, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(52, 197, 182, 0.1) 1px, transparent 1px)
              `,
                backgroundSize: "50px 50px",
              }}
            />
          </div>

          {/* Subtle particle/network motif background */}
          <div className="absolute inset-0 bg-gradient-to-br from-[#6B8EF5]/5 via-transparent to-[#6B8EF5]/10 backdrop-blur-3xl opacity-30" />

          {/* Gradient Orbs */}
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-[#6B8EF5]/10 rounded-full blur-3xl" />
          <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-[#FEC400]/15 rounded-full blur-3xl" />
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[400px] bg-[#6B8EF5]/5 rounded-full blur-3xl" />

          <div className="relative z-10 max-w-6xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, ease: [0.16, 1, 0.3, 1] }}
              className="space-y-8"
            >
              {/* Main Headline - Oversized */}
              <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold leading-tight tracking-wide">
                Big-Security Protection. Small-Business Focus.
              </h1>

              {/* Description */}
              <p className="text-lg md:text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mt-6 font-medium">
                Big-business protection for small and midsize firms—minimizing
                risk, securing operations, and keeping you focused on growth.
              </p>

              {/* Trust Indicators */}
              <div className="flex flex-wrap justify-center items-center gap-8 md:gap-12 mt-12">
                {trustIndicators.map((item, index) => {
                  const Icon = item.icon;
                  return (
                    <motion.div
                      key={item.text}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: 0.3 + index * 0.1 }}
                      className="flex items-center gap-3 text-gray-300 hover:text-white transition-colors duration-300"
                    >
                      <Icon className="h-6 w-6 text-[#6B8EF5]" />
                      <span className="text-sm md:text-base font-medium tracking-wide">
                        {item.text}
                      </span>
                    </motion.div>
                  );
                })}
              </div>

              {/* CTA Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6, duration: 0.8 }}
                className="flex flex-col gap-6 mt-16"
              >
                <div className="flex justify-center">
                  <FilloutEmbed />
                </div>
                <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                  <a
                    href="https://overview.teclara.tech"
                    target="_blank"
                    rel="noopener noreferrer"
                    onClick={handleBrochureDownload}
                    className="group w-full sm:w-64"
                    aria-label="View Teclara Overview Brochure"
                  >
                    <Button
                      variant="outline"
                      className="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 w-full sm:w-64 h-14 px-10 bg-black text-white border border-[#FF1717] text-lg font-semibold shadow-lg transition-all duration-500 transform hover:scale-105 hover:text-white hover:border-[#FF1717] hover:bg-black"
                    >
                      Teclara at a Glance
                    </Button>
                  </a>
                  <ButtonLink
                    href="/threatmap"
                    variant="outline"
                    className="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 w-full sm:w-64 h-14 px-10 bg-black text-white border border-[#FF1717] text-lg font-semibold shadow-lg transition-all duration-500 transform hover:scale-105 hover:text-white hover:border-[#FF1717] hover:bg-black"
                    aria-label="View Global Threats Map"
                  >
                    View Global Threats
                  </ButtonLink>
                </div>
              </motion.div>
            </motion.div>
          </div>

          {/* Scroll Indicator */}
          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex flex-col items-center text-white/40">
            <div className="w-6 h-10 rounded-2xl border border-[#6B8EF5]/40 flex justify-center p-1">
              <div className="w-1 h-2 bg-[#6B8EF5]/40 rounded-full animate-pulse" />
            </div>
            <p className="text-xs mt-2 tracking-wide">SCROLL</p>
          </div>
        </section>

        {/* Partner Logos Section */}
        <section className="py-16 md:py-24 border-t border-[#6B8EF5]/20 bg-gradient-to-br from-[#060d25] via-[#04081a] to-[#060d25]">
          <div className="max-w-7xl mx-auto px-6">
            <p className="text-center text-base font-medium text-gray-300 mb-12 tracking-wide">
              LEVERAGING WORLD CLASS VENDORS
            </p>
            <div className="relative overflow-hidden">
              {/* Fade gradients */}
              <div className="absolute left-0 top-0 w-32 h-full bg-gradient-to-r from-[#060d25] via-[#060d25] to-transparent z-10" />
              <div className="absolute right-0 top-0 w-32 h-full bg-gradient-to-l from-[#060d25] via-[#060d25] to-transparent z-10" />

              {/* Scrolling container */}
              <div className="flex animate-scroll">
                <div className="flex items-center space-x-8 px-4">
                  {doubledLogos.map((logo, index) => (
                    <div
                      key={`${logo.name}-${index}`}
                      className="flex-shrink-0 bg-white/90 rounded-lg p-4 border border-[#6B8EF5]/10 hover:border-[#6B8EF5]/30 transition-all duration-300"
                    >
                      <PartnerLogo
                        name={logo.name}
                        width={logo.width}
                        height={48}
                        image={logo.image}
                      />
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          <style
            dangerouslySetInnerHTML={{
              __html: `
              @keyframes scroll {
                0% { transform: translateX(0); }
                100% { transform: translateX(-50%); }
              }
              .animate-scroll {
                animation: scroll 30s linear infinite;
                width: max-content;
              }
              .animate-scroll:hover {
                animation-play-state: paused;
              }
            `,
            }}
          />
        </section>

        {/* Services Section - Feature Grid */}
        <section className="py-16 md:py-24 px-6 bg-gradient-to-br from-[#060d25] via-[#04081a] to-[#060d25]">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="text-center mb-20"
            >
              <h2 className="text-4xl md:text-6xl font-bold mb-6 tracking-wide">
                Everything Your Business Needs to{" "}
                <span className="text-[#6B8EF5]">Thrive</span>
              </h2>
              <p className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed font-medium">
                From cybersecurity to IT operations—we deliver enterprise-grade
                solutions designed for growing businesses. Complete protection,
                proactive support, and strategic guidance to fuel your success.
              </p>
            </motion.div>

            {/* Responsive grid: 1 col mobile, 2x2 tablet, 4 cols desktop */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8">
              {services.map((service, index) => {
                const Icon = service.icon;
                return (
                  <motion.div
                    key={service.title}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className="group relative"
                  >
                    {/* Card with visual variation and enhanced interactivity */}
                    <div className={`
                      bg-gradient-to-br ${service.bgGradient}
                      backdrop-blur-sm rounded-2xl p-6 lg:p-8
                      border ${service.borderColor} ${service.hoverBorderColor}
                      transition-all duration-200
                      hover:bg-[#060d25]/90 hover:-translate-y-1
                      ${service.glowColor}
                      h-full flex flex-col
                      relative overflow-hidden
                    `}>
                      {/* Subtle background pattern for visual variation */}
                      <div className="absolute inset-0 opacity-5 bg-gradient-to-br from-white/10 to-transparent pointer-events-none"></div>

                      {/* Color-coded icon */}
                      <div className={`${service.iconBg} p-4 rounded-xl inline-block mb-6 relative z-10 group-hover:scale-110 transition-transform duration-200`}>
                        <Icon className={`h-8 w-8 ${service.iconColor} group-hover:drop-shadow-lg transition-all duration-200`} />
                      </div>

                      <div className="relative z-10 flex-grow">
                        <h3 className="text-xl lg:text-2xl font-bold mb-4 tracking-wide text-white group-hover:text-white transition-colors duration-200">
                          {service.title}
                        </h3>
                        <p className="text-gray-300 mb-6 leading-relaxed font-medium text-sm lg:text-base group-hover:text-gray-200 transition-colors duration-200">
                          {service.description}
                        </p>

                        <ul className="space-y-3 mt-auto">
                          {service.features.map((feature, featureIndex) => (
                            <li
                              key={featureIndex}
                              className="flex items-start text-sm text-gray-300 group-hover:text-gray-200 transition-colors duration-200"
                            >
                              <CheckCircle className={`h-4 w-4 ${service.iconColor} mr-3 flex-shrink-0 mt-0.5 group-hover:scale-110 transition-transform duration-200`} />
                              <span className="font-medium">{feature}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>
        </section>

        {/* About Section */}
        <section className="py-16 md:py-24 px-6 bg-gradient-to-br from-[#060d25] via-[#04081a] to-[#060d25] relative overflow-hidden">
          {/* Subtle radial background glow for spatial definition */}
          <div className="absolute inset-0 bg-gradient-radial from-[#6B8EF5]/8 via-transparent to-transparent opacity-60 pointer-events-none"></div>
          <div className="max-w-7xl mx-auto relative z-10">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <div className="inline-flex items-center gap-2 bg-[#6B8EF5]/10 text-[#6B8EF5] px-6 py-3 rounded-full text-sm font-medium mb-8 border border-[#6B8EF5]/20">
                <Shield className="w-4 h-4" />
                TRUSTED CANADIAN PARTNER
              </div>
              <h2 className="text-4xl md:text-6xl font-bold mb-6 tracking-wide">
                Enterprise Expertise,{" "}
                <span className="text-[#6B8EF5]">Canadian Focus</span>
              </h2>
            </motion.div>

            {/* Content Cards */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
                className="bg-[#060d25]/80 backdrop-blur-sm rounded-2xl p-8 border border-[#6B8EF5]/20"
              >
                <h3 className="text-2xl font-extrabold text-white mb-8 tracking-wide text-center">
                  <span className="md:border-b-2 md:border-[#6B8EF5] pb-3 drop-shadow-[0_0_8px_rgba(107,142,245,0.6)]">
                    Canada's Cybersecurity Specialists
                  </span>
                </h3>
                <p className="text-gray-300 leading-relaxed text-lg font-medium">
                  <strong className="text-white">Teclara Technologies</strong> is Canada's trusted partner for managed cybersecurity and IT.
                  We specialize in protecting small and midsize businesses from the latest <span className="text-[#FF1717] font-semibold">threats</span>— delivering enterprise-grade protection,
                  compliance expertise, and business continuity as a fully managed service.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="bg-[#060d25]/80 backdrop-blur-sm rounded-2xl p-8 border border-[#6B8EF5]/20"
              >
                <h3 className="text-2xl font-extrabold text-white mb-8 tracking-wide text-center">
                  <span className="md:border-b-2 md:border-[#6B8EF5] pb-3 drop-shadow-[0_0_8px_rgba(107,142,245,0.6)]">
                    Security-First Platform
                  </span>
                </h3>
                <p className="text-gray-300 leading-relaxed text-lg font-medium">
                  Our security-first platform combines proactive threat defense with expert support and seamless technology operations.
                  Built specifically for law firms, financial services, and professional organizations.
                  Experience the confidence of having your data, reputation, and future protected— always on guard.
                </p>
              </motion.div>
            </div>

            {/* Key Stats */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12"
            >
              {keyStats.map((stat, index) => {
                const Icon = stat.icon;
                return (
                  <motion.div
                    key={stat.label}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="bg-[#060d25]/80 backdrop-blur-sm rounded-xl p-6 text-center border border-[#6B8EF5]/20 hover:border-[#6B8EF5]/60 hover:bg-[#060d25]/90 transition-all duration-500 group hover:scale-105"
                  >
                    <div className="w-14 h-14 bg-gradient-to-br from-[#6B8EF5]/20 to-[#6B8EF5]/10 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:from-[#6B8EF5]/30 group-hover:to-[#6B8EF5]/20 transition-all duration-500 shadow-lg">
                      <Icon className="w-7 h-7 text-[#7CA0FF] group-hover:text-[#8BB0FF] transition-colors duration-500" />
                    </div>
                    <div className="text-3xl font-bold text-white mb-2 group-hover:text-[#7CA0FF] transition-colors duration-500">
                      {stat.number}
                    </div>
                    <div className="text-sm text-gray-300 tracking-wide font-medium group-hover:text-gray-200 transition-colors duration-500">
                      {stat.label}
                    </div>
                  </motion.div>
                );
              })}
            </motion.div>

            {/* Industry Focus */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="bg-[#060d25]/80 backdrop-blur-sm rounded-2xl p-8 border border-[#6B8EF5]/20 mb-12"
            >
              <h4 className="text-xl font-bold text-white mb-6 text-center tracking-wide">
                Industries We Serve
              </h4>
              <div className="flex flex-wrap gap-2 justify-center">
                {industries.map((industry, index) => (
                  <span
                    key={index}
                    className="px-3 py-1.5 bg-[#060d25] rounded-full text-sm text-gray-300 border border-[#6B8EF5]/20 hover:border-[#6B8EF5]/40 hover:text-white transition-all duration-300 font-medium"
                  >
                    {industry}
                  </span>
                ))}
              </div>
            </motion.div>

            {/* CTA */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="text-center"
            >
              <ButtonLink
                href="/about"
                className="bg-black text-white border-2 border-[#FF1717] px-8 py-3 text-base font-semibold rounded-full transition-all duration-500 hover:shadow-[0_0_20px_rgba(254,196,0,0.4)] inline-flex items-center gap-3"
              >
                <TrendingUp className="w-5 h-5" />
                Learn More About Us
              </ButtonLink>
            </motion.div>
          </div>
        </section>

        <Footer />
      </div>
    </>
  );
};

export default React.memo(Index);
