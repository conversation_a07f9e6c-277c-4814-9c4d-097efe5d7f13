import React, { useState, useEffect, useCallback } from "react";
import { motion } from "framer-motion";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import PlanCard from "@/components/PlanCard";
import {
  Cloud,
  BarChart,
  Clock,
  CheckCircle,
  HelpCircle,
  Rocket,
  Check,
  ChevronDown,
  Star,
} from "lucide-react";
import { Label } from "@/components/ui/label";
import CanonicalUrl from "@/components/CanonicalUrl";
import { Helmet } from "react-helmet-async";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { trackEvent } from "@/components/analyticsUtils";
import LeadGenerationButton from "@/components/LeadGenerationButton";

const pricingDetails: Record<
  string,
  {
    annualUserPrice: number;
    year3UserPrice: number;
    serverAddonPrice: number;
  }
> = {
  Essentials: {
    annualUserPrice: 149,
    year3UserPrice: 129,
    serverAddonPrice: 99,
  },
  Professional: {
    annualUserPrice: 229,
    year3UserPrice: 199,
    serverAddonPrice: 249,
  },
  Complete: {
    annualUserPrice: 299,
    year3UserPrice: 269,
    serverAddonPrice: 299,
  },
};

const Pricing = () => {
  const [isAnnual, setIsAnnual] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);
  const [formState, setFormState] = useState({
    name: "",
    email: "",
    company: "",
    message: "",
  });
  const [submitting, setSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState("");
  const [selectedPlan, setSelectedPlan] = useState("Essentials");
  const [numUsers, setNumUsers] = useState(10);
  const [numServers, setNumServers] = useState(1);
  const [calculatedPrice, setCalculatedPrice] = useState(0);
  const [openFaq, setOpenFaq] = useState<number | null>(null);

  const features = [
    "IT Support",
    {
      name: "Remote IT Support",
      essentials: "8x5 Support",
      professional: "8x5 Support",
      complete: "24/7 Support",
    },
    {
      name: "Onsite IT Support",
      essentials: (
        <span
          title="Available as add-on per site visit"
          className="text-sm text-gray-300"
        >
          Available
        </span>
      ),
      professional: (
        <span
          title="Available as add-on per site visit"
          className="text-sm text-gray-300"
        >
          Available
        </span>
      ),
      complete: true,
    },

    "Core Protection",
    {
      name: "Email Security",
      essentials: true,
      professional: true,
      complete: true,
    },
    {
      name: "Endpoint Protection",
      essentials: true,
      professional: true,
      complete: true,
    },
    {
      name: "Email Backup",
      essentials: true,
      professional: true,
      complete: true,
    },
    {
      name: "Device & Patch Management",
      essentials: true,
      professional: true,
      complete: true,
    },
    {
      name: "Endpoint & Server Backup",
      essentials: false,
      professional: true,
      complete: true,
    },
    // Server Protection Add-On (per server) section
    "Server Protection Add-On (per server)",
    {
      name: "Full endpoint protection",
      essentials: true,
      professional: true,
      complete: true,
    },
    {
      name: "Vulnerability & patch management",
      essentials: true,
      professional: true,
      complete: true,
    },
    {
      name: "Server backup (full image or VSS-based)",
      essentials: false,
      professional: true,
      complete: true,
    },
    {
      name: "Monitoring & alerting (resource utilization, failures)",
      essentials: true,
      professional: true,
      complete: true,
    },
    {
      name: "Critical Response",
      essentials: "5x8",
      professional: "24/7",
      complete: "24/7",
    },

    "Cloud & Identity",
    {
      name: "Cloud Management",
      essentials: "Microsoft/Google Workspace",
      professional: "Includes Cloud Infrastructure",
      complete: "Multi-Tenant & Hybrid",
    },
    {
      name: "Entra ID Backup",
      essentials: false,
      professional: true,
      complete: true,
    },

    "Security Hardening",
    {
      name: "Security Training",
      essentials: "Online Training",
      professional: "Training + Phishing + Risk Scoring",
      complete: "Training + Phishing + Risk Scoring",
    },
    {
      name: "Vulnerability Management",
      essentials: false,
      professional: true,
      complete: true,
    },
    {
      name: "Incident Response",
      essentials: false,
      professional: true,
      complete: true,
    },
    {
      name: "24×7 SOC Monitoring",
      essentials: false,
      professional: true,
      complete: true,
    },
    {
      name: "SIEM",
      essentials: false,
      professional: (
        <span
          title="Lightweight SIEM suitable for SMBs and straightforward environments"
          className="text-sm text-gray-300"
        >
          SIEM for SMB
        </span>
      ),
      complete: (
        <span
          title="Advanced SIEM designed for enterprise and complex multi-cloud environments"
          className="text-sm text-gray-300"
        >
          SIEM for Enterprise
        </span>
      ),
    },

    "Strategic & Compliance",
    {
      name: "Strategic IT Planning",
      essentials: false,
      professional: false,
      complete: (
        <div className="text-sm text-gray-300 whitespace-pre-line">
          Quarterly strategy reviews,
          <br />
          roadmap planning, and
          <br />
          exec reporting
        </div>
      ),
    },
    {
      name: "Compliance Reporting",
      essentials: false,
      professional: false,
      complete: (
        <div className="text-sm text-gray-300 whitespace-pre-line">
          Reports for regulatory
          <br />
          audits and cyber
          <br />
          insurance
        </div>
      ),
    },
  ];


  const faqs = [
    {
      question: "What's included in each plan?",
      answer:
        "All plans include IT support, device protection, and cloud security features. Essentials and Professional plans include 8x5 support, while the Complete plan includes 24/7 support. As you move up tiers, you unlock SOC monitoring, SIEM, vulnerability management, compliance reporting, and strategic IT planning.",
    },
    {
      question: "How does billing work?",
      answer:
        "You can choose between 1-year or 3-year terms. Annual billing provides predictable costs. Opting for a 3-year term saves you 20%.",
    },
    {
      question: "Can I add features later?",
      answer:
        "Yes. You can upgrade your plan or add optional services like onsite support, additional tenants, or dedicated CISO consulting at any time.",
    },
    {
      question: "Is this suitable for regulated industries?",
      answer:
        "Yes. The Complete tier includes compliance-ready reporting, SIEM for complex environments, and quarterly IT planning designed for legal, financial, and healthcare sectors.",
    },
    {
      question: "How quickly can we get started?",
      answer:
        "Most clients are fully onboarded within 1–2 weeks. We handle the entire process including system audit, agent deployment, and user training.",
    },
    {
      question: "What about data backup and recovery?",
      answer:
        "All plans include email backup. Professional and Complete tiers add endpoint and server backup with point-in-time recovery. We maintain multiple backup copies and regularly test recovery procedures.",
    },
    {
      question: "Do you offer custom solutions?",
      answer:
        "Yes. While our plans are designed to cover most business needs, we can customize solutions for specific requirements. This includes specialized compliance needs, custom integrations, or unique security requirements.",
    },
    {
      question: "What happens if we need to scale up?",
      answer:
        "Our solutions are designed to scale with your business. You can easily upgrade your plan or add additional services as your needs grow. We'll help you plan the transition to ensure minimal disruption.",
    },
    {
      question: "What kind of support do you provide?",
      answer:
        "Essentials and Professional plans include 8x5 remote IT support, while the Complete plan includes 24/7 support. All plans can add onsite support as needed.",
    },
  ];

  const calculatePrice = useCallback(() => {
    const details = pricingDetails[selectedPlan];
    if (!details) return;

    // Ensure a minimum of 10 users for Essentials and Professional plans for calculation
    const effectiveNumUsers =
      selectedPlan === "Essentials" || selectedPlan === "Professional"
        ? Math.max(10, numUsers)
        : numUsers;

    const userCost = isAnnual
      ? effectiveNumUsers * details.annualUserPrice
      : effectiveNumUsers * details.year3UserPrice;

    // Complete plan includes 2 servers per 10 users, others include 1 server per 10 users
    const serversPerTenUsers = selectedPlan === "Complete" ? 2 : 1;
    const includedServers = Math.floor(numUsers / 10) * serversPerTenUsers;
    const extraServers = Math.max(0, numServers - includedServers);
    const serverAddonCost = extraServers * details.serverAddonPrice;

    const totalCost = userCost + serverAddonCost;
    setCalculatedPrice(totalCost);
  }, [selectedPlan, numUsers, numServers, isAnnual]);

  useEffect(() => {
    calculatePrice();
  }, [calculatePrice]);

  useEffect(() => {
    if (!modalOpen) return;
    let submitted = false;
    const handleSubmit = () => {
      submitted = true;
    };
    const form = document.getElementById("pricing-modal-form");
    form?.addEventListener("submit", handleSubmit);
    return () => {
      // Removed form abandon tracking - not essential for business insights
      form?.removeEventListener("submit", handleSubmit);
    };
  }, [modalOpen]);

  const handleFormChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setFormState({ ...formState, [e.target.name]: e.target.value });
  };

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    setError("");
    setSuccess(false);
    try {
      const formData = new FormData();
      formData.append("name", formState.name);
      formData.append("email", formState.email);
      formData.append("company", formState.company);
      formData.append("message", formState.message);
      formData.append("source", "from pricing popup");
      // Form submission removed - using Fillout forms instead
      // Redirect to thank you page
      window.location.href = "/thank-you";
    } catch (err) {
      setError("There was a problem submitting your form. Please try again.");
    } finally {
      setSubmitting(false);
    }
  };




  const pricingSection = (
    <section id="pricing-plans" className="relative pt-44 pb-16 md:pb-24 px-6 bg-gradient-to-br from-[#060d25] via-[#04081a] to-[#060d25] overflow-hidden">
      {/* Red Glow Background Effect */}
      <div className="pointer-events-none absolute -top-32 left-1/2 -translate-x-1/2 w-[700px] h-[400px] bg-red-500/30 rounded-full blur-3xl z-0" />

      <div className="max-w-7xl mx-auto relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-6xl font-bold mb-6 tracking-wide">
            Choose Your <span className="text-[#6B8EF5]">Protection Level</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto font-medium leading-relaxed">
            All plans include IT support, security monitoring, and the
            flexibility to scale with your business needs.
          </p>

          {/* Billing Toggle */}
          <div className="mt-8 max-w-md mx-auto">
            <div className="bg-[#060d25]/80 backdrop-blur-sm border border-[#6B8EF5]/20 rounded-xl p-1 flex">
              <button
                onClick={() => setIsAnnual(true)}
                className={`flex-1 px-4 py-3 rounded-lg text-sm font-medium transition-all duration-300 ${
                  isAnnual
                    ? "bg-[#6B8EF5] text-white shadow-lg"
                    : "text-gray-300 hover:text-white hover:bg-[#6B8EF5]/20"
                }`}
              >
                1 Year Term
              </button>
              <button
                onClick={() => setIsAnnual(false)}
                className={`flex-1 px-4 py-3 rounded-lg text-sm font-medium transition-all duration-300 relative ${
                  !isAnnual
                    ? "bg-[#FF1717] text-white shadow-lg"
                    : "text-gray-300 hover:text-white hover:bg-[#FF1717]/20"
                }`}
              >
                <span className="block">3 Year Term</span>
                <span className="block text-xs mt-0.5 opacity-90">
                  Save 20%
                </span>
              </button>
            </div>
          </div>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          <PlanCard
            name="Essentials"
            price={isAnnual ? "149" : "129"}
            description="Core protection & IT support for small teams"
            features={[
              { name: "Remote IT Support", included: true },
              { name: "Email Security", included: true },
              { name: "Endpoint Protection", included: true },
              { name: "Email Backup", included: true },
              { name: "Device & Patch Management", included: true },
              { name: "Cloud Management", included: true },
              { name: "Security Training", included: false },
              { name: "Endpoint & Server Backup", included: false },
              { name: "Vulnerability Management", included: false },
              { name: "Incident Response", included: false },
              { name: "24×7 SOC Monitoring", included: false },
              { name: "Basic SIEM", included: false },
              { name: "Entra ID Backup", included: false },
              { name: "Strategic IT Planning", included: false },
              { name: "Compliance Reporting", included: false },
            ]}
            darkMode={true}
            includeServerInfo={true}
            serverAddonPrice={
              <>
                <span className="font-semibold">+ $99/server/month</span>{" "}
                <span className="text-[10px] align-middle">
                  (server add-on)
                </span>
              </>
            }
            cta={{ label: "Get Started – For Small Teams", href: "#" }}
            buttonClassName="bg-black text-white border-2 border-[#FF1717] px-8 py-3 text-base font-semibold rounded-full transition-all duration-500 hover:shadow-[0_0_20px_rgba(254,196,0,0.4)]"
          />
          <PlanCard
            name="Professional"
            price={isAnnual ? "229" : "199"}
            description="Advanced security & IT management for growth"
            features={[
              { name: "Remote IT Support", included: true },
              { name: "Email Security", included: true },
              { name: "Endpoint Protection", included: true },
              { name: "Email Backup", included: true },
              { name: "Device & Patch Management", included: true },
              { name: "Cloud Management", included: true },
              { name: "Security Training", included: true },
              { name: "Endpoint & Server Backup", included: true },
              { name: "Vulnerability Management", included: true },
              { name: "Incident Response", included: true },
              { name: "24×7 SOC Monitoring", included: true },
              { name: "Basic SIEM", included: true },
              { name: "Entra ID Backup", included: true },
              { name: "Strategic IT Planning", included: false },
              { name: "Compliance Reporting", included: false },
            ]}
            popular={true}
            darkMode={true}
            includeServerInfo={true}
            serverAddonPrice={
              <>
                <span className="font-semibold">+ $249/server/month</span>{" "}
                <span className="text-[10px] align-middle">
                  (server add-on)
                </span>
              </>
            }
            cta={{ label: "Get Started", href: "#" }}
            buttonClassName="bg-black text-white border-2 border-[#FF1717] px-8 py-3 text-base font-semibold rounded-full transition-all duration-500 hover:shadow-[0_0_20px_rgba(254,196,0,0.4)]"
          />
          <PlanCard
            name="Complete"
            price={isAnnual ? "299" : "269"}
            description="Enterprise-grade security & managed IT"
            features={[
              { name: "Remote & Onsite IT Support", included: true },
              { name: "Email Security", included: true },
              { name: "Endpoint Protection", included: true },
              { name: "Email Backup", included: true },
              { name: "Device & Patch Management", included: true },
              { name: "Cloud Management", included: true },
              { name: "Security Training", included: true },
              { name: "Endpoint & Server Backup", included: true },
              { name: "Vulnerability Management", included: true },
              { name: "Incident Response", included: true },
              { name: "24×7 SOC Monitoring", included: true },
              { name: "Full SIEM", included: true },
              { name: "Entra ID Backup", included: true },
              { name: "Strategic IT Planning", included: true },
              { name: "Compliance Reporting", included: true },
            ]}
            darkMode={true}
            includeServerInfo={true}
            serverInclusionText="✓ Includes 2 servers per 10 users at no extra cost"
            serverAddonPrice={
              <>
                <span className="font-semibold">+ $299/server/month</span>{" "}
                <span className="text-[10px] align-middle">
                  (server add-on)
                </span>
              </>
            }
            cta={{ label: "Get Started – Full Protection", href: "#" }}
            buttonClassName="bg-black text-white border-2 border-[#FF1717] px-8 py-3 text-base font-semibold rounded-full transition-all duration-500 hover:shadow-[0_0_20px_rgba(254,196,0,0.4)]"
          />
        </div>

        {/* Pricing Calculator */}
        <div className="max-w-3xl mx-auto bg-[#060d25]/80 backdrop-blur-sm p-8 rounded-2xl border border-[#6B8EF5]/20 mt-16">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold mb-3 text-white tracking-wide">
              ESTIMATE YOUR COST
            </h3>
            <p className="text-gray-300 font-medium">
              Calculate the estimated monthly price based on your needs.
            </p>
          </div>
          <div className="mb-8 max-w-md mx-auto">
            <div className="bg-[#060d25]/80 backdrop-blur-sm border border-[#6B8EF5]/20 rounded-xl p-1 flex">
              <button
                onClick={() => {
                  setIsAnnual(true);
                  trackEvent("calculator_change", {
                    category: "Calculator",
                    label: "Term",
                    plan: selectedPlan,
                    users: numUsers,
                    servers: numServers,
                    term: "1-year",
                  });
                }}
                className={`flex-1 px-4 py-3 rounded-lg text-sm font-medium transition-all duration-300 ${
                  isAnnual
                    ? "bg-[#6B8EF5] text-white shadow-lg"
                    : "text-gray-300 hover:text-white hover:bg-[#6B8EF5]/20"
                }`}
              >
                1 Year Term
              </button>
              <button
                onClick={() => {
                  setIsAnnual(false);
                  trackEvent("calculator_change", {
                    category: "Calculator",
                    label: "Term",
                    plan: selectedPlan,
                    users: numUsers,
                    servers: numServers,
                    term: "3-year",
                  });
                }}
                className={`flex-1 px-4 py-3 rounded-lg text-sm font-medium transition-all duration-300 relative ${
                  !isAnnual
                    ? "bg-[#FF1717] text-white shadow-lg"
                    : "text-gray-300 hover:text-white hover:bg-[#FF1717]/20"
                }`}
              >
                <span className="block">3 Year Term</span>
                <span className="block text-xs mt-0.5 opacity-90">
                  Save 20%
                </span>
              </button>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div>
              <Label
                htmlFor="plan-select"
                className="block text-sm font-medium text-white mb-2"
              >
                Select Plan
              </Label>
              <select
                id="plan-select"
                value={selectedPlan}
                onChange={(e) => {
                  setSelectedPlan(e.target.value);
                  trackEvent("calculator_change", {
                    category: "Calculator",
                    label: "Plan",
                    plan: e.target.value,
                    users: numUsers,
                    servers: numServers,
                    term: !isAnnual ? "1-year" : "3-year",
                  });
                }}
                className="w-full px-3 py-2 border border-[#6B8EF5]/30 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-[#6B8EF5] focus:border-[#6B8EF5] bg-[#060d25] text-white"
              >
                <option value="Essentials">Essentials</option>
                <option value="Professional">Professional</option>
                <option value="Complete">Complete</option>
              </select>
            </div>
            <div>
              <Label
                htmlFor="users-input"
                className="block text-sm font-medium text-white mb-2"
              >
                Number of Users
              </Label>
              <input
                type="number"
                id="users-input"
                value={numUsers}
                onChange={(e) => {
                  const value = Math.max(0, parseInt(e.target.value, 10) || 0);
                  setNumUsers(value);
                  trackEvent("calculator_change", {
                    category: "Calculator",
                    label: "Users",
                    plan: selectedPlan,
                    users: value,
                    servers: numServers,
                    term: !isAnnual ? "1-year" : "3-year",
                  });
                }}
                min="0"
                className="w-full px-3 py-2 border border-[#6B8EF5]/30 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-[#6B8EF5] focus:border-[#6B8EF5] bg-[#060d25] text-white"
              />
            </div>
            <div>
              <Label
                htmlFor="servers-input"
                className="block text-sm font-medium text-white mb-2"
              >
                Number of Servers
              </Label>
              <input
                type="number"
                id="servers-input"
                value={numServers}
                onChange={(e) => {
                  const value = Math.max(0, parseInt(e.target.value, 10) || 0);
                  setNumServers(value);
                  trackEvent("calculator_change", {
                    category: "Calculator",
                    label: "Servers",
                    plan: selectedPlan,
                    users: numUsers,
                    servers: value,
                    term: !isAnnual ? "1-year" : "3-year",
                  });
                }}
                min="0"
                className="w-full px-3 py-2 border border-[#6B8EF5]/30 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-[#6B8EF5] focus:border-[#6B8EF5] bg-[#060d25] text-white"
              />
            </div>
          </div>
          <div className="text-center text-3xl font-bold text-[#6B8EF5] bg-[#060d25] border border-[#6B8EF5]/30 p-6 rounded-xl shadow-sm">
            Estimated Monthly Cost: ${calculatedPrice.toFixed(2)}
          </div>

          {/* Legal Disclaimer */}
          <div className="text-center text-xs text-gray-500 mt-4">
            <strong>
              This is an estimate only and does not constitute a binding contract or quote.
            </strong>
            <br />
            Final pricing may vary based on specific requirements and implementation details.
            <br />
            <span className="italic">
              Please contact us for official pricing and a detailed proposal tailored to your needs.
            </span>
          </div>

          {numUsers < 10 &&
            (selectedPlan === "Essentials" ||
              selectedPlan === "Professional") && (
              <div className="text-center text-sm text-gray-500 mt-4">
                * Minimum charge for 10 users applies for Essentials and
                Professional plans.
              </div>
            )}
        </div>

        <div className="text-xs text-gray-500 text-center mt-10">
          <strong>
            Essentials & Professional plans include 1 server per 10 users. Complete plan includes 2 servers per 10 users at no extra cost.
          </strong>{" "}
          Additional servers are billed at the add-on rate.
          <br />
          <span className="italic">
            Example: A 20-user Complete plan gets 4 included servers; Essentials/Professional get 2 included servers.
          </span>
        </div>
      </div>
    </section>
  );

  const featuresTableSection = (
    <section id="features-table" className="py-16 md:py-24 bg-gradient-to-br from-[#060d25] via-[#04081a] to-[#060d25]">
      <div className="container">
        <div className="text-center max-w-2xl mx-auto mb-16">
          <div className="inline-flex items-center bg-[#6B8EF5]/10 text-[#6B8EF5] text-sm font-medium px-4 py-2 rounded-full mb-6">
            <Check className="h-4 w-4 mr-2" />
            Feature Comparison
          </div>
          <h2 className="text-3xl font-bold mb-4 text-white tracking-wide">
            No Surprises About The Price
          </h2>
          <p className="text-gray-300 font-medium">
            Core features comparison across all plans
          </p>
        </div>

        {/* Comparison table - Hidden on mobile */}
        <div className="max-w-6xl mx-auto pt-4 hidden md:block">
          <div className="bg-[#060d25] rounded-2xl shadow-2xl border border-[#6B8EF5]/20 relative overflow-hidden">
            {/* Table Header */}
            <div className="bg-[#060d25] border-b border-[#6B8EF5]/20 rounded-t-2xl">
              <div className="grid grid-cols-4 gap-0 pt-4">
                <div className="p-6 border-r border-[#6B8EF5]/20">
                  <div className="text-lg font-semibold text-white">
                    Core features
                  </div>
                </div>
                <div className="p-6 text-center border-r border-[#6B8EF5]/20">
                  <div className="text-lg font-semibold text-white mb-2">
                    Essentials
                  </div>
                  <div className="text-2xl font-bold text-[#6B8EF5]">
                    ${isAnnual ? "149" : "129"}{" "}
                    <span className="text-sm font-normal text-gray-400">
                      / month
                    </span>
                  </div>
                  <LeadGenerationButton
                    formType="IT_CONSULT"
                    location="Pricing Page Comparison Table"
                    buttonText="Choose Essentials"
                    className="w-full mt-4 bg-black text-white border-2 border-[#FF1717] py-2 rounded-full font-medium text-sm transition-all duration-500 hover:shadow-[0_0_20px_rgba(254,196,0,0.4)]"
                  />
                </div>
                <div className="p-6 text-center border-r border-[#6B8EF5]/20 bg-gradient-to-br from-[#6B8EF5] to-[#5B7FE8] text-white relative">
                  <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 z-10">
                    <div className="bg-[#060d25] text-[#6B8EF5] text-xs font-semibold px-3 py-1 rounded-full border border-[#6B8EF5]/30">
                      MOST POPULAR
                    </div>
                  </div>
                  <div className="text-lg font-semibold mb-2 mt-2">
                    Professional
                  </div>
                  <div className="text-2xl font-bold">
                    ${isAnnual ? "229" : "199"}{" "}
                    <span className="text-sm font-normal text-gray-200">
                      / month
                    </span>
                  </div>
                  <LeadGenerationButton
                    formType="IT_CONSULT"
                    location="Pricing Page Comparison Table"
                    buttonText="Choose Professional"
                    className="w-full mt-4 bg-black text-white border-2 border-[#FF1717] py-2 rounded-full font-medium text-sm transition-all duration-500 hover:shadow-[0_0_20px_rgba(254,196,0,0.4)]"
                  />
                </div>
                <div className="p-6 text-center">
                  <div className="text-lg font-semibold text-white mb-2">
                    Complete
                  </div>
                  <div className="text-2xl font-bold text-[#6B8EF5]">
                    ${isAnnual ? "299" : "269"}{" "}
                    <span className="text-sm font-normal text-gray-400">
                      / month
                    </span>
                  </div>
                  <LeadGenerationButton
                    formType="IT_CONSULT"
                    location="Pricing Page Comparison Table"
                    buttonText="Choose Complete"
                    className="w-full mt-4 bg-black text-white border-2 border-[#FF1717] py-2 rounded-full font-medium text-sm transition-all duration-500 hover:shadow-[0_0_20px_rgba(254,196,0,0.4)]"
                  />
                </div>
              </div>
            </div>

            {/* Table Body */}
            <div className="divide-y divide-[#6B8EF5]/20">
              {features.map((feature, idx) => {
                if (typeof feature === "string") {
                  // Section header
                  return (
                    <div
                      key={`header-${feature}`}
                      className="bg-[#6B8EF5]/10 border-y border-[#6B8EF5]/20"
                    >
                      <div className="p-4">
                        <h4 className="text-base font-semibold text-[#6B8EF5] tracking-wide">
                          {feature}
                        </h4>
                      </div>
                    </div>
                  );
                } else {
                  // Feature row
                  return (
                    <div
                      key={
                        typeof feature.name === "string" ? feature.name : idx
                      }
                      className="hover:bg-[#6B8EF5]/5 transition-colors duration-150"
                    >
                      <div className="grid grid-cols-4 gap-0">
                        <div className="p-4 border-r border-[#6B8EF5]/20">
                          <div className="text-sm font-medium text-white">
                            {feature.name}
                          </div>
                        </div>
                        <div className="p-4 text-center border-r border-[#6B8EF5]/20">
                          {typeof feature.essentials === "boolean" ? (
                            feature.essentials ? (
                              <CheckCircle className="h-5 w-5 text-[#6B8EF5] mx-auto" />
                            ) : (
                              <div className="h-5 w-5 rounded-full border-2 border-gray-500 mx-auto"></div>
                            )
                          ) : (
                            <div className="text-xs text-gray-300 max-w-[100px] mx-auto">
                              {feature.essentials}
                            </div>
                          )}
                        </div>
                        <div className="p-4 text-center border-r border-[#6B8EF5]/20 bg-[#6B8EF5]/5">
                          {typeof feature.professional === "boolean" ? (
                            feature.professional ? (
                              <CheckCircle className="h-5 w-5 text-[#6B8EF5] mx-auto" />
                            ) : (
                              <div className="h-5 w-5 rounded-full border-2 border-gray-500 mx-auto"></div>
                            )
                          ) : (
                            <div className="text-xs text-gray-300 max-w-[100px] mx-auto font-medium">
                              {feature.professional}
                            </div>
                          )}
                        </div>
                        <div className="p-4 text-center">
                          {typeof feature.complete === "boolean" ? (
                            feature.complete ? (
                              <CheckCircle className="h-5 w-5 text-[#6B8EF5] mx-auto" />
                            ) : (
                              <div className="h-5 w-5 rounded-full border-2 border-gray-500 mx-auto"></div>
                            )
                          ) : (
                            <div className="text-xs text-gray-300 max-w-[100px] mx-auto">
                              {feature.complete}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                }
              })}
            </div>
          </div>

          {/* Additional info */}
          <div className="text-center mt-8">
            <p className="text-sm text-gray-500">
              <strong>
                Essentials & Professional plans include 1 server per 10 users. Complete plan includes 2 servers per 10 users at no extra cost.
              </strong>{" "}
              Additional servers are billed at the add-on rate.
            </p>
            <p className="text-xs text-gray-400 mt-2 italic">
              Example: A 20-user Complete plan gets 4 included servers; Essentials/Professional get 2 included servers.
            </p>
          </div>
        </div>
      </div>
    </section>
  );

  const faqSection = (
    <section className="py-16 md:py-24 bg-gradient-to-br from-[#060d25] via-[#04081a] to-[#060d25]">
      <div className="container">
        <div className="text-center max-w-2xl mx-auto mb-16">
          <div className="inline-flex items-center bg-[#6B8EF5]/10 text-[#6B8EF5] text-sm font-medium px-4 py-2 rounded-full mb-6">
            <HelpCircle className="h-4 w-4 mr-2" />
            Common Questions
          </div>
          <h2 className="text-3xl font-bold mb-4 text-white tracking-wide">
            Frequently Asked Questions
          </h2>
          <p className="text-gray-300 font-medium">
            Everything you need to know about our pricing and services.
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <div
                key={index}
                className="border border-[#6B8EF5]/20 rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow bg-[#060d25]/50 backdrop-blur-sm"
              >
                <button
                  className="w-full flex justify-between items-center text-left py-6 px-6 focus:outline-none focus-visible:ring-2 focus-visible:ring-[#6B8EF5] rounded-xl bg-transparent hover:bg-[#6B8EF5]/5 transition-colors duration-300"
                  onClick={() => {
                    setOpenFaq(openFaq === index ? null : index);
                    trackEvent("faq_toggle", {
                      category: "FAQ",
                      label: faq.question,
                      action: openFaq === index ? "collapse" : "expand",
                    });
                  }}
                  aria-expanded={openFaq === index}
                  aria-controls={`faq-answer-${index}`}
                >
                  <h3 className="text-lg font-medium text-white">
                    {faq.question}
                  </h3>
                  <ChevronDown
                    className={`h-5 w-5 text-[#6B8EF5] transform transition-transform duration-300 ${
                      openFaq === index ? "rotate-180" : ""
                    }`}
                  />
                </button>
                <div
                  id={`faq-answer-${index}`}
                  className={`overflow-hidden transition-all duration-500 ease-in-out ${
                    openFaq === index ? "max-h-96" : "max-h-0"
                  }`}
                >
                  <div className="px-6 pb-6">
                    <p className="text-gray-300 leading-relaxed">
                      {faq.answer}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );

  const clearProcessSection = (
    <section className="py-16 md:py-24 bg-gradient-to-br from-[#060d25] via-[#04081a] to-[#060d25]">
      <div className="container mx-auto">
        <div className="text-center mb-12">
          <div className="inline-flex items-center bg-[#6B8EF5]/10 text-[#6B8EF5] text-sm font-medium px-4 py-2 rounded-full mb-6">
            <Rocket className="h-4 w-4 mr-2" />
            Simple Process
          </div>
          <h2 className="text-3xl font-bold text-white mb-4 tracking-wide">
            Clear Process, Transparent Pricing
          </h2>
          <p className="text-lg text-gray-300 max-w-4xl mx-auto font-medium">
            Our streamlined onboarding process and transparent pricing make it
            easy to get started—no negotiations, no back-and-forth.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {[
            {
              icon: <BarChart className="h-8 w-8 text-[#6B8EF5]" />,
              title: "Choose Your Plan",
              description:
                "Select from clear, pre-built service packages that fit your needs. No customization or negotiations required—just simple, transparent pricing with everything included.",
            },
            {
              icon: <Clock className="h-8 w-8 text-[#6B8EF5]" />,
              title: "Fast Implementation",
              description:
                "Our expert team quickly configures your systems with minimal disruption. Dedicated support guides you every step of the way for a smooth transition.",
            },
            {
              icon: <Cloud className="h-8 w-8 text-[#6B8EF5]" />,
              title: "Transparent & Scalable",
              description:
                "No hidden fees or surprises—what you see is what you pay. Easily scale your service level as your business grows with seamless upgrades and no penalties.",
            },
          ].map((feature, index) => (
            <div
              key={index}
              className="bg-[#060d25]/80 backdrop-blur-sm p-8 rounded-2xl shadow-lg border border-[#6B8EF5]/20 hover:shadow-xl hover:border-[#6B8EF5]/40 transition-all duration-300 hover:-translate-y-1"
            >
              <div className="h-14 w-14 rounded-full bg-[#6B8EF5]/10 flex items-center justify-center mb-6">
                {feature.icon}
              </div>
              <h3 className="text-xl font-semibold mb-3 text-white tracking-wide">
                {feature.title}
              </h3>
              <p className="text-gray-300 leading-relaxed font-medium">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );

  const ctaSection = (
    <section className="py-16 md:py-24 bg-gradient-to-br from-[#060d25] via-[#04081a] to-[#060d25] text-white relative overflow-hidden">
      {/* Background pattern */}
      <div className="absolute inset-0 opacity-10">
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `
            linear-gradient(rgba(52, 197, 182, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(52, 197, 182, 0.1) 1px, transparent 1px)
          `,
            backgroundSize: "50px 50px",
          }}
        />
      </div>

      <div className="relative container mx-auto px-4">
        <div className="text-center max-w-2xl mx-auto">
          <div className="inline-flex items-center bg-[#6B8EF5]/10 backdrop-blur-sm text-[#6B8EF5] text-sm font-medium px-4 py-2 rounded-full mb-6">
            <Star className="h-4 w-4 mr-2" />
            Get Started Today
          </div>
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-white tracking-wide">
            Ready To Get Started?
          </h2>
          <p className="text-lg text-gray-300 mb-8 leading-relaxed font-medium">
            Book a consultation to discuss your security needs and find the
            right plan for your business.
          </p>
          <LeadGenerationButton
            formType="IT_CONSULT"
            location="Pricing Page Bottom CTA"
            buttonText="Schedule Your Free Consultation"
            className="bg-black text-white border-2 border-[#FF1717] px-8 py-3 text-base font-semibold rounded-full transition-all duration-500 hover:shadow-[0_0_20px_rgba(254,196,0,0.4)]"
          />
        </div>
      </div>
    </section>
  );

  return (
    <>
      <Helmet>
        <title>
          Pricing for Cybersecurity & IT in Oakville, Hamilton, Milton, St.
          Catharines & Halton Region | Teclara Technologies
        </title>
        <meta
          name="description"
          content="Transparent flat-rate pricing for enterprise cybersecurity and managed IT services. No hidden fees, predictable costs, comprehensive protection starting at competitive rates."
        />
      </Helmet>
      <CanonicalUrl path="/pricing" />
      <div className="min-h-screen bg-gradient-to-br from-[#060d25] via-[#04081a] to-[#060d25] text-white">
        <Navbar />

        {pricingSection}
        {featuresTableSection}
        {faqSection}
        {clearProcessSection}
        {ctaSection}

        <Footer />
      </div>
      <Dialog open={modalOpen} onOpenChange={setModalOpen}>
        <DialogContent className="bg-gradient-to-br from-[#060d25] to-[#04081a] border border-[#6B8EF5]/20">
          <DialogHeader>
            <DialogTitle className="text-white">Get Started with Teclara</DialogTitle>
          </DialogHeader>
          {success ? (
            <div className="text-[#6B8EF5] font-semibold text-center py-8">
              Thank you! We'll be in touch shortly.
            </div>
          ) : (
            <form
              id="pricing-modal-form"
              className="space-y-6"
              onSubmit={handleFormSubmit}
            >
              <input type="hidden" name="source" value="from pricing popup" />
              <div className="space-y-2">
                <label
                  htmlFor="name"
                  className="block text-sm font-medium text-gray-700"
                >
                  Name
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  required
                  value={formState.name}
                  onChange={handleFormChange}
                  className="w-full px-4 py-2 border border-[#6B8EF5]/30 rounded-md focus:ring-2 focus:ring-[#6B8EF5] focus:border-[#6B8EF5] bg-[#060d25] text-white placeholder-gray-400"
                  placeholder="Enter your name"
                />
              </div>
              <div className="space-y-2">
                <label
                  htmlFor="email"
                  className="block text-sm font-medium text-white"
                >
                  Email
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  required
                  value={formState.email}
                  onChange={handleFormChange}
                  className="w-full px-4 py-2 border border-[#6B8EF5]/30 rounded-md focus:ring-2 focus:ring-[#6B8EF5] focus:border-[#6B8EF5] bg-[#060d25] text-white placeholder-gray-400"
                  placeholder="Enter your email"
                />
              </div>
              <div className="space-y-2">
                <label
                  htmlFor="company"
                  className="block text-sm font-medium text-white"
                >
                  Company
                </label>
                <input
                  type="text"
                  id="company"
                  name="company"
                  required
                  value={formState.company}
                  onChange={handleFormChange}
                  className="w-full px-4 py-2 border border-[#6B8EF5]/30 rounded-md focus:ring-2 focus:ring-[#6B8EF5] focus:border-[#6B8EF5] bg-[#060d25] text-white placeholder-gray-400"
                  placeholder="Enter your company name"
                />
              </div>
              <div className="space-y-2">
                <label
                  htmlFor="message"
                  className="block text-sm font-medium text-white"
                >
                  Message
                </label>
                <textarea
                  id="message"
                  name="message"
                  rows={5}
                  required
                  value={formState.message}
                  onChange={handleFormChange}
                  className="w-full px-4 py-2 border border-[#6B8EF5]/30 rounded-md focus:ring-2 focus:ring-[#6B8EF5] focus:border-[#6B8EF5] bg-[#060d25] text-white placeholder-gray-400"
                  placeholder="How can we help you?"
                />
              </div>
              {error && (
                <div className="text-red-400 text-sm text-center">{error}</div>
              )}
              <button
                type="submit"
                className="w-full bg-black text-white border-2 border-[#FF1717] px-6 py-3 text-base font-semibold rounded-full transition-all duration-500 hover:shadow-[0_0_20px_rgba(254,196,0,0.4)]"
                disabled={submitting}
              >
                {submitting ? "Submitting..." : "Submit"}
              </button>
            </form>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default Pricing;
