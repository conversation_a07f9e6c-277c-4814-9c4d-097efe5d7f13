import React from 'react';
import { motion } from 'framer-motion';
import { 
  Shield, 
  Activity, 
  FileCheck, 
  Users, 
  Clock, 
  Lock, 
  AlertCircle, 
  CheckCircle, 
  ArrowRight, 
  Send,
  Monitor,
  Cloud,
  GraduationCap,
  Server,
  Mail
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { useToast } from '@/hooks/use-toast';
import { Link } from 'react-router-dom';
import PartnerLogo from '@/components/PartnerLogo';
import { Helmet } from 'react-helmet-async';
import CanonicalUrl from '@/components/CanonicalUrl';
import { trackFormSubmission } from '@/components/analyticsUtils';
import { ButtonLink } from '@/components/ui/button-link';
import {
  createLocalBusinessSchema,
  createServiceSchema,
  createFAQSchema
} from '@/utils/schemaData';
import LeadGenerationButton from '@/components/LeadGenerationButton';
import { FilloutStandardEmbed } from '@fillout/react';

// Form schema for the contact form
const contactFormSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters." }),
  email: z.string().email({ message: "Please enter a valid email address." }),
  company: z.string().min(1, { message: "Company name is required." }),
  phone: z.string().min(10, { message: "Please enter a valid phone number." }),
});

const CybersecurityLanding = () => {
  const { toast } = useToast();
  const buildNumber = import.meta.env.VITE_BUILD_NUMBER 
    ? import.meta.env.VITE_BUILD_NUMBER.substring(0, 7)
    : 'local';
  
  // Form handling
  const form = useForm<z.infer<typeof contactFormSchema>>({
    resolver: zodResolver(contactFormSchema),
    defaultValues: {
      name: "",
      email: "",
      company: "",
      phone: ""
    },
  });

  // Removed form abandon tracking - focusing on conversion events only

  // Create FAQ data
  const securityFAQs = [
    {
      question: "What cybersecurity services do you offer for small businesses?",
      answer: "We provide comprehensive cybersecurity solutions including 24/7 monitoring, endpoint protection, network security, staff training, and compliance support, all tailored for small and medium businesses."
    },
    {
      question: "How do you protect against ransomware?",
      answer: "Our multi-layered approach includes advanced endpoint protection, regular backups, network segmentation, and staff training to prevent, detect, and recover from ransomware attacks."
    },
    {
      question: "What compliance standards do you support?",
      answer: "We support various compliance standards including PIPEDA, PCI DSS, and industry-specific regulations, helping businesses achieve and maintain compliance through comprehensive security measures."
    },
    {
      question: "How quickly can you respond to security incidents?",
      answer: "Our 24/7 security operations center provides immediate response to security incidents, with automated detection and rapid response protocols to minimize impact and restore operations quickly."
    }
  ];

  // Create schema data
  const localBusinessSchema = createLocalBusinessSchema({
    name: "Teclara Technologies",
    description: "Enterprise-grade cybersecurity solutions for Canadian SMBs",
    address: {
      street: "123 Business Street",
      city: "Oakville",
      region: "ON",
      postalCode: "L6H 0C3",
      country: "CA"
    },
    geo: {
      latitude: 43.4675,
      longitude: -79.6877
    },
    phone: "******-123-4567",
    email: "<EMAIL>",
    priceRange: "$$",
    openingHours: [
      "Monday 09:00-17:00",
      "Tuesday 09:00-17:00",
      "Wednesday 09:00-17:00",
      "Thursday 09:00-17:00",
      "Friday 09:00-17:00"
    ]
  });

  const serviceSchema = createServiceSchema(
    "Enterprise-Grade Cybersecurity Solutions",
    "Comprehensive cybersecurity solutions for small and medium businesses in Ontario, including 24/7 monitoring, staff training, and compliance support.",
    "Cybersecurity Services",
    {
      areaServed: ["Oakville", "Hamilton", "Milton", "St. Catharines", "Halton Region"],
      serviceOutput: [
        "24/7 Security Monitoring",
        "Endpoint Protection",
        "Network Security",
        "Staff Training",
        "Compliance Support",
        "Incident Response",
        "Vulnerability Management",
        "Security Assessments",
        "Data Protection",
        "Business Continuity"
      ]
    }
  );

  const faqSchema = createFAQSchema(securityFAQs);

  return (
    <>
      <Helmet>
        <title>Cybersecurity for Small Businesses in Oakville, Hamilton, Milton, St. Catharines & Halton Region | Teclara Technologies</title>
        <meta name="description" content="Advanced cybersecurity solutions for small businesses: AI-powered threat detection, 24/7 SOC monitoring, ransomware protection, and compliance support to secure your operations." />
        
        {/* Page specific Open Graph */}
        <meta property="og:title" content="Enterprise-Grade Cybersecurity Solutions | Teclara Technologies" />
        <meta property="og:description" content="Protect your business with advanced threat detection, compliance support, and expert security monitoring from local specialists." />
        <meta property="og:url" content="https://teclara.tech/cybersecurity" />
        <meta property="og:type" content="website" />
        <meta property="og:image" content="https://teclara.tech/uploads/teclara_logo_black_text.png" />
        <meta property="og:image:alt" content="Teclara Technologies Cybersecurity Solutions" />
        
        {/* Page specific Twitter */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="Enterprise-Grade Cybersecurity Solutions | Teclara Technologies" />
        <meta name="twitter:description" content="Protect your business with enterprise-grade cybersecurity solutions. 24/7 monitoring, compliance support, and expert threat response from local specialists." />
        <meta name="twitter:image" content="https://teclara.tech/uploads/teclara_logo_black_text.png" />
        
        {/* Additional meta tags */}
        <meta name="keywords" content="cybersecurity, small business security, endpoint protection, network security, compliance, data protection, ransomware protection, Oakville, Hamilton, Milton, St. Catharines, Halton Region" />
        <meta name="author" content="Teclara Technologies" />
        <meta name="robots" content="index, follow" />
        <meta name="language" content="en-CA" />
        <meta name="geo.region" content="CA-ON" />
        <meta name="geo.placename" content="Oakville, Ontario" />
        <meta name="geo.position" content="43.4675;-79.6877" />
        <meta name="ICBM" content="43.4675, -79.6877" />
        
        {/* Resource hints */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        
        {/* Schema.org data */}
        <script type="application/ld+json">
          {JSON.stringify(localBusinessSchema)}
        </script>
        <script type="application/ld+json">
          {JSON.stringify(serviceSchema)}
        </script>
        <script type="application/ld+json">
          {JSON.stringify(faqSchema)}
        </script>
      </Helmet>
      <CanonicalUrl path="/cybersecurity" />
      
      <main className="min-h-screen">
        {/* Logo Section */}
        <div className="py-8 bg-[#1F252F]">
          <div className="container mx-auto px-4">
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="flex justify-center"
            >
              <Link to="/">
                <img 
                  src="/uploads/teclara_logo_white_text.png" 
                  alt="Teclara Logo" 
                  className="h-12 w-auto"
                />
              </Link>
            </motion.div>
          </div>
        </div>

        {/* Hero Section */}
        <section className="relative py-24 md:py-32 overflow-hidden bg-gradient-to-b from-[#1F252F] to-[#2A2A2A] text-white">
          <div className="absolute inset-0 bg-grid-white/[0.05] bg-[length:20px_20px]" aria-hidden="true" />
          
          {/* Animated background elements */}
          <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-teclara-primary/20 rounded-full blur-3xl opacity-20" aria-hidden="true" />
          <div className="absolute bottom-0 left-0 w-1/4 h-1/4 bg-teal-400/20 rounded-full blur-3xl opacity-20" aria-hidden="true" />
          
          <div className="container mx-auto px-4 relative z-10">
            <motion.div 
              className="max-w-4xl mx-auto text-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, ease: "easeOut" }}
            >
              <h1 className="text-4xl md:text-6xl font-bold mb-6 tracking-tight">
                Cybersecurity That Works for <span className="text-teclara-primary">Small Businesses</span>, Not Just Enterprises
              </h1>
              <p className="text-xl md:text-2xl text-gray-300 mb-10 max-w-3xl mx-auto">
                Get enterprise-grade protection without the enterprise price tag. We make cybersecurity simple and effective for Ontario's small businesses.
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <LeadGenerationButton
                  formType="SECURITY_REVIEW"
                  location="Cybersecurity Landing Hero"
                />
              </div>
            </motion.div>
          </div>
        </section>

        {/* Key Benefits Section */}
        <section className="relative py-24 overflow-hidden">
          {/* Animated Background */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-teal-50">
            {/* Animated circles */}
            <motion.div
              className="absolute top-1/4 left-1/4 w-64 h-64 bg-teclara-primary/5 rounded-full blur-3xl"
              animate={{
                scale: [1, 1.2, 1],
                x: [0, 30, 0],
                y: [0, -30, 0],
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
            <motion.div
              className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl"
              animate={{
                scale: [1, 1.3, 1],
                x: [0, -40, 0],
                y: [0, 40, 0],
              }}
              transition={{
                duration: 10,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
            {/* Grid Pattern */}
            <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTAgMTBMMCAwTTEwIDEwTDIwIDBNMTAgMTBMMCAyME0xMCAxMEwyMCAyMCIgc3Ryb2tlPSIjMDAwIiBzdHJva2Utb3BhY2l0eT0iMC4wMiIgc3Ryb2tlLXdpZHRoPSIxIi8+PC9zdmc+')] bg-[length:20px_20px]" />
          </div>

          <div className="container mx-auto px-4 relative z-10">
            <motion.div 
              className="text-center mb-16"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.7 }}
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Complete Protection for Your Business
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Our turnkey cybersecurity solution includes everything you need to protect your business, from threat monitoring to staff training.
              </p>
            </motion.div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {[
                {
                  icon: <Monitor className="h-12 w-12 text-blue-400" />,
                  title: "24/7 Threat Monitoring",
                  description: "SOC + MDR services that watch over your business around the clock, detecting and responding to threats in real-time.",
                  color: "from-blue-50 to-blue-100/50",
                  iconBg: "bg-blue-100",
                  iconColor: "text-blue-600",
                  delay: 0.1
                },
                {
                  icon: <Cloud className="h-12 w-12 text-purple-400" />,
                  title: "Microsoft + Google Protection",
                  description: "Comprehensive security for your cloud workspace, including email protection, data loss prevention, and access control.",
                  color: "from-purple-50 to-purple-100/50",
                  iconBg: "bg-purple-100",
                  iconColor: "text-purple-600",
                  delay: 0.2
                },
                {
                  icon: <GraduationCap className="h-12 w-12 text-green-400" />,
                  title: "Security Awareness Training",
                  description: "Regular training sessions to help your staff identify and prevent common security threats like phishing and social engineering.",
                  color: "from-green-50 to-green-100/50",
                  iconBg: "bg-green-100",
                  iconColor: "text-green-600",
                  delay: 0.3
                },
                {
                  icon: <Server className="h-12 w-12 text-cyan-400" />,
                  title: "Advanced Security Tools",
                  description: "SIEM, EDR, and vulnerability scanning to identify and address security gaps before they can be exploited.",
                  color: "from-cyan-50 to-cyan-100/50",
                  iconBg: "bg-cyan-100",
                  iconColor: "text-cyan-600",
                  delay: 0.4
                }
              ].map((feature, index) => (
                <motion.div 
                  key={index}
                  className={`relative overflow-hidden rounded-2xl bg-gradient-to-br ${feature.color} p-8 shadow-lg border border-gray-100/50 hover:shadow-xl transition-all duration-300`}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: feature.delay }}
                  whileHover={{ y: -5 }}
                >
                  {/* Animated Background Elements */}
                  <motion.div
                    className="absolute -right-4 -bottom-4 w-24 h-24 bg-white/10 rounded-full blur-2xl"
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [0.3, 0.5, 0.3],
                    }}
                    transition={{
                      duration: 4,
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: index * 0.2
                    }}
                  />
                  <motion.div
                    className="absolute -left-4 -top-4 w-24 h-24 bg-white/10 rounded-full blur-2xl"
                    animate={{
                      scale: [1, 1.3, 1],
                      opacity: [0.3, 0.6, 0.3],
                    }}
                    transition={{
                      duration: 5,
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: index * 0.3
                    }}
                  />
                  
                  {/* Content */}
                  <div className="relative z-10">
                    <motion.div 
                      className={`${feature.iconBg} ${feature.iconColor} p-4 rounded-xl inline-block mb-4`}
                      whileHover={{ scale: 1.1 }}
                      transition={{ type: "spring", stiffness: 400, damping: 10 }}
                    >
                      {feature.icon}
                    </motion.div>
                    <h3 className="text-xl font-semibold mb-3 text-gray-900">{feature.title}</h3>
                    <p className="text-gray-700">{feature.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Floating Stats */}
            <motion.div 
              className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.7, delay: 0.7 }}
            >
              {[
                { number: "24/7", label: "Threat Monitoring" },
                { number: "< 15min", label: "Response Time" },
                { number: "99.9%", label: "Detection Rate" }
              ].map((stat, index) => (
                <motion.div
                  key={index}
                  className="bg-white/80 backdrop-blur-sm p-6 rounded-xl border border-gray-100 text-center"
                  whileHover={{ y: -5 }}
                  transition={{ type: "spring", stiffness: 400, damping: 10 }}
                >
                  <motion.div
                    className="text-3xl font-bold text-[#FF1919] mb-2"
                    animate={{ scale: [1, 1.1, 1] }}
                    transition={{ duration: 2, repeat: Infinity, delay: index * 0.2 }}
                  >
                    {stat.number}
                  </motion.div>
                  <div className="text-gray-600">{stat.label}</div>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </section>

        {/* Why Choose Us Section */}
        <section className="relative py-24 overflow-hidden bg-gradient-to-b from-gray-900 via-gray-800 to-gray-900">
          {/* Animated Background Elements */}
          <div className="absolute inset-0">
            {/* Animated circles */}
            <motion.div
              className="absolute top-1/4 left-1/4 w-96 h-96 bg-teclara-primary/10 rounded-full blur-3xl"
              animate={{
                scale: [1, 1.2, 1],
                x: [0, 30, 0],
                y: [0, -30, 0],
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
            <motion.div
              className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"
              animate={{
                scale: [1, 1.3, 1],
                x: [0, -40, 0],
                y: [0, 40, 0],
              }}
              transition={{
                duration: 10,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
            {/* Grid Pattern */}
            <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTAgMTBMMCAwTTEwIDEwTDIwIDBNMTAgMTBMMCAyME0xMCAxMEwyMCAyMCIgc3Ryb2tlPSIjZmZmIiBzdHJva2Utb3BhY2l0eT0iMC4wNSIgc3Ryb2tlLXdpZHRoPSIxIi8+PC9zdmc+')] bg-[length:20px_20px]" />
          </div>

          <div className="container mx-auto px-4 relative z-10">
            <div className="max-w-5xl mx-auto text-center mb-16">
              <motion.h2 
                className="text-3xl md:text-4xl font-bold text-white mb-4"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.7 }}
              >
                Built for SMBs. Trusted by Cyber Insurers.
              </motion.h2>
              <motion.p 
                className="text-xl text-gray-200 mb-8"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.7, delay: 0.1 }}
              >
                Unlike generic security vendors, we take full responsibility for your protection. Teclara's security stack is aligned to insurer expectations, regulatory standards, and real-world SMB needs.
              </motion.p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mt-12">
                {[
                  {
                    title: "SentinelOne",
                    description: "Endpoint Protection & EDR",
                    icon: <Shield className="h-8 w-8 text-[#FF1919]" />,
                    bgColor: "from-gray-800/90 to-gray-900/90",
                    iconBg: "bg-[#FF1919]/20",
                    iconColor: "text-[#FF1919]",
                    features: ["AI-Powered Protection", "Real-time Response", "Zero Trust Security"],
                    gradient: "from-[#FF1919]/20 to-transparent"
                  },
                  {
                    title: "Avanan",
                    description: "Email Security & Protection",
                    icon: <Mail className="h-8 w-8 text-[#FF1919]" />,
                    bgColor: "from-gray-800/90 to-gray-900/90",
                    iconBg: "bg-[#FF1919]/20",
                    iconColor: "text-[#FF1919]",
                    features: ["Cloud Email Security", "Phishing Protection", "Data Loss Prevention"],
                    gradient: "from-[#FF1919]/20 to-transparent"
                  },
                  {
                    title: "usecure",
                    description: "Security Awareness Training",
                    icon: <GraduationCap className="h-8 w-8 text-[#FF1919]" />,
                    bgColor: "from-gray-800/90 to-gray-900/90",
                    iconBg: "bg-[#FF1919]/20",
                    iconColor: "text-[#FF1919]",
                    features: ["Phishing Simulations", "Security Training", "Compliance Modules"],
                    gradient: "from-[#FF1919]/20 to-transparent"
                  },
                  {
                    title: "Microsoft",
                    description: "Cloud Security & Compliance",
                    icon: <Cloud className="h-8 w-8 text-[#FF1919]" />,
                    bgColor: "from-gray-800/90 to-gray-900/90",
                    iconBg: "bg-[#FF1919]/20",
                    iconColor: "text-[#FF1919]",
                    features: ["Defender Suite", "Conditional Access", "Compliance Tools"],
                    gradient: "from-[#FF1919]/20 to-transparent"
                  }
                ].map((partner, index) => (
                  <motion.div 
                    key={index}
                    className={`relative overflow-hidden rounded-2xl bg-gradient-to-br ${partner.bgColor} p-8 shadow-lg border border-white/10 hover:border-[#FF1919]/30 transition-all duration-300 backdrop-blur-sm group`}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    whileHover={{ y: -5, scale: 1.02 }}
                  >
                    {/* Gradient Overlay */}
                    <div className={`absolute inset-0 bg-gradient-to-br ${partner.gradient} opacity-0 group-hover:opacity-100 transition-opacity duration-300`} />
                    
                    {/* Background Pattern */}
                    <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTAgMTBMMCAwTTEwIDEwTDIwIDBNMTAgMTBMMCAyME0xMCAxMEwyMCAyMCIgc3Ryb2tlPSIjZmZmIiBzdHJva2Utb3BhY2l0eT0iMC4wMiIgc3Ryb2tlLXdpZHRoPSIxIi8+PC9zdmc+')] bg-[length:20px_20px]" />
                    
                    {/* Content */}
                    <div className="relative z-10">
                      <motion.div 
                        className={`${partner.iconBg} ${partner.iconColor} p-3 rounded-xl inline-block mb-4 transform group-hover:scale-110 transition-transform duration-300`}
                        whileHover={{ rotate: 5 }}
                      >
                        {partner.icon}
                      </motion.div>
                      <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-[#FF1919] transition-colors duration-300">{partner.title}</h3>
                      <p className="text-gray-300 mb-4 group-hover:text-gray-200 transition-colors duration-300">{partner.description}</p>
                      
                      {/* Features List */}
                      <ul className="space-y-2">
                        {partner.features.map((feature, featureIndex) => (
                          <motion.li 
                            key={featureIndex} 
                            className="flex items-center text-sm text-gray-400 group-hover:text-gray-300 transition-colors duration-300"
                            initial={{ opacity: 0, x: -10 }}
                            whileInView={{ opacity: 1, x: 0 }}
                            transition={{ delay: featureIndex * 0.1 }}
                          >
                            <CheckCircle className="h-4 w-4 text-[#FF1919] mr-2 flex-shrink-0 group-hover:scale-110 transition-transform duration-300" />
                            <span>{feature}</span>
                          </motion.li>
                        ))}
                      </ul>
                    </div>
                    
                    {/* Decorative Elements */}
                    <div className="absolute -right-4 -bottom-4 w-24 h-24 bg-[#FF1919]/5 rounded-full blur-2xl group-hover:bg-[#FF1919]/10 transition-colors duration-300" />
                    <div className="absolute -left-4 -top-4 w-24 h-24 bg-[#FF1919]/5 rounded-full blur-2xl group-hover:bg-[#FF1919]/10 transition-colors duration-300" />
                  </motion.div>
                ))}
              </div>

              {/* Trust Indicators */}
              <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
                {[
                  {
                    title: "24/7 Monitoring",
                    description: "Round-the-clock threat detection and response",
                    icon: <Activity className="h-6 w-6 text-[#FF1919]" />
                  },
                  {
                    title: "CyberSecure Canada",
                    description: "Government-certified security provider",
                    icon: <Shield className="h-6 w-6 text-[#FF1919]" />
                  },
                  {
                    title: "Insurer Approved",
                    description: "Aligned with cyber insurance requirements",
                    icon: <FileCheck className="h-6 w-6 text-[#FF1919]" />
                  }
                ].map((indicator, index) => (
                  <motion.div
                    key={index}
                    className="flex items-center justify-center space-x-4 bg-white/5 backdrop-blur-sm p-6 rounded-xl border border-white/10 hover:border-[#FF1919]/30 transition-all duration-300 group"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    whileHover={{ y: -5, scale: 1.02 }}
                  >
                    <div className="bg-[#FF1919]/20 p-3 rounded-lg group-hover:bg-[#FF1919]/30 transition-colors duration-300">
                      {React.cloneElement(indicator.icon, { className: "h-6 w-6 text-[#FF1919] group-hover:scale-110 transition-transform duration-300" })}
                    </div>
                    <div>
                      <h4 className="font-semibold text-white group-hover:text-[#FF1919] transition-colors duration-300">{indicator.title}</h4>
                      <p className="text-sm text-gray-300 group-hover:text-gray-200 transition-colors duration-300">{indicator.description}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section - Ready to Secure Your Business */}
        <section className="bg-[#0A0A0A] border-t border-[#333] py-16 text-white">
          <div className="container mx-auto px-4 max-w-7xl">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-16">
              {/* Left: Info & Contact */}
              <div>
                <h2 className="text-3xl md:text-4xl font-extrabold mb-4 text-white">Ready to Secure Your Business?</h2>
                <p className="text-lg text-gray-300 mb-8">Get started with a free security assessment. Our experts will analyze your current security posture and provide personalized recommendations.</p>
                <div className="mb-8">
                  <h3 className="text-xl font-bold mb-2 text-white">Get in Touch</h3>
                  <p className="mb-2 text-gray-300">Schedule a consultation with our cybersecurity experts. We'll discuss your specific needs and show you how Teclara Technologies can protect your organization.</p>
                  <div className="space-y-1 text-base">
                    <div>
                      <span className="font-semibold text-white">Email Us</span>
                      <br />
                      <a href="mailto:<EMAIL>" className="text-blue-400 hover:text-blue-300 transition-colors"><EMAIL></a>
                    </div>
                    <div className="mt-2">
                      <span className="font-semibold text-white">Call Us</span>
                      <br />
                      <a href="tel:+13659965856" className="text-blue-400 hover:text-blue-300 transition-colors">(*************</a>
                    </div>
                  </div>
                </div>
                <div className="mb-8">
                  <h3 className="text-lg font-bold mb-2 text-white">Why Start With Us?</h3>
                  <ul className="list-disc list-inside text-gray-300 space-y-1">
                    <li>Free security assessment (no obligation)</li>
                    <li>30-minute expert consultation</li>
                    <li>Custom security roadmap</li>
                    <li>Implementation timeline & pricing</li>
                  </ul>
                </div>
              </div>
              {/* Right: Contact Form */}
              <div>
                <div className="bg-[#181C23] rounded-2xl p-8 shadow-lg border border-gray-700">
                  <h3 className="text-2xl font-bold mb-6 text-white">Request Your Free Assessment</h3>
                  <div className="w-full" style={{ height: '600px' }}>
                    <FilloutStandardEmbed
                      filloutId="38CVFXhpBfus"
                      inheritParameters
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      
      {/* Minimal Footer */}
      <footer className="bg-[#0A0A0A] border-t border-[#333] py-8">
        <div className="container mx-auto px-4">
          <div className="flex flex-col items-center space-y-4">
            {/* Logo */}
            <div className="flex items-center space-x-2">
              <img 
                src="/uploads/teclara_logo_white_text.png" 
                alt="Teclara Logo" 
                className="h-8 w-auto"
              />
            </div>

            {/* Copyright */}
            <p className="text-white/70 text-sm">
              © 2025 Teclara Technologies Inc. | <a href="/privacy-policy" className="hover:text-white transition-colors">Privacy Policy</a> | <a href="/terms-of-service" className="hover:text-white transition-colors">Terms</a> | <a href="mailto:<EMAIL>" className="hover:text-white transition-colors">Contact</a>
            </p>
            <p className="text-white/70 text-sm">
              Build: <span className="italic">{buildNumber}</span>
            </p>
          </div>
        </div>
      </footer>
    </>
  );
};

export default CybersecurityLanding; 